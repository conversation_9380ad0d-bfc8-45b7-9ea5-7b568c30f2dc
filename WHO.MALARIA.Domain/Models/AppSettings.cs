﻿namespace WHO.MALARIA.Domain.Models
{
    public class AppSettings
    {
        public bool ShowErrorDetails { get; set; }
        public string SupportUrl { get; set; }
        public string SupportEmail { get; set; }
        public int StatsPollingInterval { get; set; }
        public long QueuePollingInterval { get; set; }
        public int UpdatePasswordLinkExpiry { get; set; }
        public short CookieExpireTimeSpan { get; set; }

        public short TokenRefreshThreshold { get; set; }

        public bool RestrictBusinessEntityModification { get; set; }

        public AnalyticsFields AnalyticsFields { get; set; }

        // setting for sirilog
        public Serilog Serilog { get; set; }

        // Azure client id and secrete
        public Azure AzureAD { get; set; }

        // Email sending related settings
        public Email Email { get; set; }

        public SendGrid SendGrid { get; set; }

        public Cloudmersive Cloudmersive { get; set; }

        // Identity Server configuration
        public IdentityServer IdentityServer { get; set; }
    }

    public class Serilog
    {
        public MinimumLevel MinimumLevel { get; set; }
        public string[] Enrich { get; set; }
        public string OutputTemplate { get; set; }
        public string LogTableName { get; set; }
        public string SerilogFilePath { get; set; }
    }

    public class AnalyticsFields
    {
        public string TrackingId { get; set; }     
        public string PrivateKey { get; set; }
        public string ClientEmail { get; set; }       
        public string WebsiteCode { get; set; }     
    }

    public class MinimumLevel
    {
        public string Default { get; set; }
        public Override Override { get; set; }
    }

    public class Override
    {
        public string Microsoft { get; set; }
        public string System { get; set; }
    }

    public class Azure
    {
        public string ClientId { get; set; }

        public string ClientSecret { get; set; }

        public string TenantId { get; set; }

        public string BaseUrl { get; set; }
    }

    public class Email
    {
        public string EmailSender { get; set; }
        public string SmtpHost { get; set; }
        public int SmtpPort { get; set; }
        public string SmtpUserName { get; set; }
        public string SmtpPassword { get; set; }
        public string UserName { get; set; }
        public string Password { get; set; }
        public string SendGridApiKey { get; set; }
    }

    public class SendGrid
    {
        public string ApiKey { get; set; }
        public string From { get; set; }
    }

    public class Cloudmersive
    {
        public string ApiKey { get; set; }
    }

    public class IdentityServer
    {
        public string BaseUrl { get; set; }
    }
}
