﻿using FluentValidation.Results;

using MediatR;

using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;

using Serilog;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.DeskReview;
using WHO.MALARIA.Features.Helpers;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Assessment;
using WHO.MALARIA.Services.Rules.Shared;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles the incoming request to Add/Update assessment indicator response for desk review
    /// </summary>
    public class CreateOrUpdateIndicatorAssessmentDeskReviewCommandHandler : RuleBase, IRequestHandler<CreateOrUpdateIndicatorAssessmentDeskReviewCommand, bool>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ITranslationService _translationService;
        private readonly IAssessmentRuleChecker _assessmentRuleChecker;

        public CreateOrUpdateIndicatorAssessmentDeskReviewCommandHandler(IUnitOfWork unitOfWork, ITranslationService translationService, IAssessmentRuleChecker assessmentRuleChecker)
        {
            _unitOfWork = unitOfWork;
            _translationService = translationService;
            _assessmentRuleChecker = assessmentRuleChecker;
        }

        /// <summary>
        /// Performs validations on the request, adds the new assessment response if it does not exist else updates it
        /// </summary>
        /// <param name="request">Contains assessment details to add/update</param>
        /// <param name="cancellationToken">Notifies to cancel the operation</param>
        /// <returns>TRUE if assessment details gets saved/updated else throw an error if any validation fails</returns>
        public async Task<bool> Handle(CreateOrUpdateIndicatorAssessmentDeskReviewCommand request, CancellationToken cancellationToken)
        {
            //Check business rule
            CheckRule(new IsDeskReviewResponseStatusValid(_translationService, request.Status));
            CheckRule(new UserShouldHaveOperationPermissionOnAssessmentRule(_translationService, _assessmentRuleChecker, request.AssessmentId, request.CurrentUserId, UserAssessmentPermission.CanSaveOrFinalizeDRIndicatorResponse));

            Type responseType;
            try
            {
                responseType = DeskReviewResponseHelper.GetResponseType(request.IndicatorId, request.StrategyId);
            }
            catch (KeyNotFoundException ex)
            {
                Log.Error(ex, ex.Message);
                throw new KeyNotFoundException(_translationService.GetTranslatedMessage(Constants.Exception.ResponseMappingKeyIsNotFound));
            }



            object responseJsonObject = JsonSerializer.Deserialize(request.Response, responseType, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters = { new JsonConverter.StringToDecimalConverter() , new JsonConverter.StringToIntConverter() }
            });

            // This code block is executed to get the value of the cannotBeAssessed property from responseJsonObject and store that value in the DB.
            Type responseObject = responseJsonObject.GetType();
            IList<PropertyInfo> propertyInfos = new List<PropertyInfo>(responseObject.GetProperties());
            bool cannotBeAssessed = false;
            PropertyInfo propertyInfo = propertyInfos.Where(props => props.Name.ToLower() == "cannotbeassessed").FirstOrDefault();
            if (propertyInfo != null)
            {
                cannotBeAssessed = (bool)propertyInfo.GetValue(responseJsonObject, null);
            }

            byte? metNotMetStatus = null;
            PropertyInfo propertyMetNotMetStatus = propertyInfos.Where(props => props.Name.ToLower() == "metnotmetstatus").FirstOrDefault();
            if (propertyMetNotMetStatus != null)
            {
                if (Enum.TryParse<MetNotMetStatus>(Convert.ToString(propertyMetNotMetStatus.GetValue(responseJsonObject, null)), out MetNotMetStatus metNotMetStatusValue))
                {
                    metNotMetStatus = (byte)metNotMetStatusValue;
                }
            }

            //check if IResponseValidator interface is implemented
            Type responseValidatorInterfaceType = responseType.GetInterface(nameof(IResponseValidator));

            AssessmentDRResponse assessmentDRResponse = await _unitOfWork.AssessmentDRResponseRepository.Queryable(x => x.AssessmentIndicatorId == request.AssessmentIndicatorId
                                                                                                                        && x.AssessmentStrategyId == request.AssessmentStrategyId)
                                                                                                        .SingleOrDefaultAsync();

            if (responseValidatorInterfaceType != null && (assessmentDRResponse?.Status == (int)DeskReviewAssessmentResponseStatus.Completed || request.Status == (int)DeskReviewAssessmentResponseStatus.Completed))
            {
                MethodInfo validator = responseType.GetMethod("Validate");

                ValidationResult result = validator.Invoke(responseJsonObject, null) as ValidationResult;

                if (!result.IsValid)
                {
                    CheckRule(new DeskReviewResponseValidationRule(result.Errors[0].ErrorMessage));
                }
            }

            string responseJsonString = responseType.GetMethod("ToString").Invoke(responseJsonObject, null)?.ToString();



            if (assessmentDRResponse == null)
            {

                assessmentDRResponse = new AssessmentDRResponse(request.AssessmentIndicatorId,
                                                                request.AssessmentStrategyId,
                                                                request.Status,
                                                                responseJsonString, cannotBeAssessed, metNotMetStatus);

                _unitOfWork.AssessmentDRResponseRepository.Add(assessmentDRResponse);
            }
            else
            {
                //check if indicator is already in completed state
                if (assessmentDRResponse.Status != (int)DeskReviewAssessmentResponseStatus.Completed)
                {
                    assessmentDRResponse.Status = request.Status;
                }

                assessmentDRResponse.CannotBeAssessed = cannotBeAssessed;

                assessmentDRResponse.MetNotMetStatus = metNotMetStatus;

                assessmentDRResponse.UpdateResponseJson(responseJsonString);

                _unitOfWork.AssessmentDRResponseRepository.Update(assessmentDRResponse);
            }

            //Add documents for those indicators who capture them
            if (request.Documents != null && request.Documents.Any())
            {
                await AddDocuments(request.Documents, assessmentDRResponse.Id);
            }

            //Delete uploaded documents
            if (request.DeletedDocumentIds != null && request.DeletedDocumentIds.Any())
            {
                await _unitOfWork.ResponseDocumentRepository.DeleteAsync(request.DeletedDocumentIds);
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return true;
        }

        /// <summary>
        /// Add documents that are uploaded during response capture of the indicator.
        /// </summary>
        /// <param name="kvpFiles">Document files</param>
        /// <param name="assessmentDRResponseId">Desk review response id</param>
        private async Task AddDocuments(IEnumerable<KeyValuePair<Guid, IFormFile>> kvpFiles, Guid assessmentDRResponseId)
        {
            if (kvpFiles.Any(x => x.Key == Guid.Empty || x.Value == null))
            {
                CheckRule(new IsFileNullCheckRule(_translationService, null, Constants.Exception.InvalidDiagramFile));
            }

            foreach (KeyValuePair<Guid, IFormFile> file in kvpFiles)
            {
                CheckRule(new IsFileExtensionValidRule(_translationService, Path.GetExtension(file.Value.FileName), Constants.Common.AllowedDiagramFileTypes));
                CheckRule(new FileSizeCheckRule(_translationService, file.Value.Length, Constants.Common.DiagramMaxFileSize, Constants.Exception.FileSizeGreaterThan10MB));
            }

            IEnumerable<Guid> documentIds = kvpFiles.Select(x => x.Key);
            IEnumerable<Guid> existingFileIds = await _unitOfWork.ResponseDocumentRepository.FindIdsAsync(assessmentDRResponseId, documentIds);

            foreach (var (kvpFile, responseDocument) in from KeyValuePair<Guid, IFormFile> kvpFile in kvpFiles
                                                        let responseDocument = new ResponseDocument(kvpFile.Key,
                                                                                                     assessmentDRResponseId,
                                                                                                     kvpFile.Value.GetBytesAsync().Result,
                                                                                                     Path.GetExtension(kvpFile.Value.FileName),
                                                                                                     kvpFile.Value.FileName)
                                                        select (kvpFile, responseDocument))
            {
                if (existingFileIds.Any(id => id == kvpFile.Key))
                {
                    _unitOfWork.ResponseDocumentRepository.Update(responseDocument);
                    continue;
                }

                _unitOfWork.ResponseDocumentRepository.Add(responseDocument);
            }
        }
    }
}
