
using Duende.IdentityServer;
using Duende.IdentityServer.Models;
using System.Collections.Generic;
using WHO.MALARIA.Domain.Models;

namespace WHO.MALARIA.Web.Areas.Idp.Configuration
{
    /// <summary>
    /// Identity Server Configuration and setup
    /// </summary>
    public static class IdentityServerConfig
    {
        /// <summary>
        /// Gets the list of clients that can use the Identity server instance for authentication
        /// </summary>
        /// <param name="appSettings">Application settings containing Identity Server configuration</param>
        /// <returns>List of registered clients</returns>
        public static IEnumerable<Client> GetClients(AppSettings appSettings)
        {
            string baseUrl = appSettings?.IdentityServer?.BaseUrl ?? "https://localhost:5001";

            return new List<Client>
            {
                new Client
                {
                    ClientId = "MALARIA",
                    ClientName = "MALARIA",
                    RedirectUris = { $"{baseUrl}/signin-oidc"},
                    FrontChannelLogoutUri = $"{baseUrl}/signout-oidc",
                    PostLogoutRedirectUris = { $"{baseUrl}/signout-callback" },
                    AllowedScopes =
                    {
                        IdentityServerConstants.StandardScopes.OpenId,
                        IdentityServerConstants.StandardScopes.Profile
                    },
                    AlwaysIncludeUserClaimsInIdToken = false,
                    RequireConsent = false,
                    AllowOfflineAccess = true,
                    RequireClientSecret = false,
                    AllowedGrantTypes = GrantTypes.Hybrid,
                    AlwaysSendClientClaims=true
                }
            };
        }

        public static IEnumerable<IdentityResource> GetIdentityResources()
        {
            return new List<IdentityResource>
            {
                new IdentityResources.OpenId(),
                new IdentityResources.Profile()
            };
        }

        public static IEnumerable<ApiResource> GetApis()
        {
            return new List<ApiResource>
            {
                new ApiResource("malaria", "External MALARIA")
            };
        }
    }
}
