{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=malsuramtkdb-20250205;User=SA;Password=**********;Connect Timeout=0;TrustServerCertificate=True;"
  },
  "AllowedHosts": "*",
  "SerilogFilePath": {
    "FilePath": "FatalLogs/FatalError"
  },
  "SerilogOutputFileTemplate": {
    "outputTemplate": "{Timestamp:yyyy-MM-dd HH:mm:ss.fff zzz} [{Level:u3}] {Message:lj}{NewLine}{Exception}"
  },
  "Serilog": {
    "MinimumLevel": {
      "Default": "Error",
      "Override": {
        "Microsoft": "Error",
        "System": "Error"
      }
    },
    "Enrich": ["FromLogContext", "WithMachineName", "WithExceptionDetails"]
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AppSettings": {
    "ShowErrorDetails": true,
    "SupportUrl": "",
    "SupportEmail": "",
    "StatsPollingInterval": 3000,
    "QueuePollingInterval ": 600000000,
    "UpdatePasswordLinkExpiry": "48",
    "CookieExpireTimeSpan": 15,
    "TokenRefreshThreshold": 300,
    "RestrictBusinessEntityModification": true,
    "AzureAD": {
      ////production keys////
      // "ClientId": "91cbb59c-add3-4d6d-8463-7835e17dcf75",
      // "ClientSecret": "****************************************",
      // "TenantId": "f610c0b7-bd24-4b39-810b-3dc280afb590"
      //// local, dev, test keys////

      "ClientId": "7cd2419e-ad71-4894-923f-136deabc4d01",
      "ClientSecret": "****************************************",
      "TenantId": "76d22fc8-2330-45cf-ab36-51074cf8f1e2"
    },
    "Email": {
      "EmailSender": "<EMAIL>",
      "SmtpHost": "smtp.live.com",
      "SmtpPort": "587",
      "UserName": "<EMAIL>",
      "Password": "malaria@12345"
    },
    "SendGrid": {
      "From": "<EMAIL>",
      "ApiKey": "*********************************************************************"
    },
    "Cloudmersive": {
      "ApiKey": "************************************"
    },
    "AnalyticsFields": {
      "TrackingId": "xxxx",
      "PrivateKey": "xxxx",
      "ClientEmail": "xxxx",
      "WebsiteCode": "xxxx"
    },
    "IdentityServer": {
      "BaseUrl": "https://localhost:5001"
    }
  }
}
